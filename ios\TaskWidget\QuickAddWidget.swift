import WidgetKit
import SwiftUI

struct QuickAddWidget: Widget {
    let kind: String = "QuickAddWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: QuickAddProvider()) { entry in
            QuickAddWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("Quick Add Task")
        .description("Quickly add a new task")
        .supportedFamilies([.systemSmall])
    }
}

struct QuickAddProvider: TimelineProvider {
    func placeholder(in context: Context) -> QuickAddEntry {
        QuickAddEntry(date: Date())
    }

    func getSnapshot(in context: Context, completion: @escaping (QuickAddEntry) -> ()) {
        let entry = QuickAddEntry(date: Date())
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        let currentDate = Date()
        let entry = QuickAddEntry(date: currentDate)
        
        // Update once a day
        let nextUpdate = Calendar.current.date(byAdding: .day, value: 1, to: currentDate)!
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        
        completion(timeline)
    }
}

struct QuickAddEntry: TimelineEntry {
    let date: Date
}

struct QuickAddWidgetEntryView: View {
    var entry: QuickAddProvider.Entry

    var body: some View {
        Link(destination: URL(string: "nextlevel://quickadd")!) {
            ZStack {
                Color(red: 0.12, green: 0.12, blue: 0.12)
                
                VStack(spacing: 8) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title)
                        .foregroundColor(Color(red: 0.09, green: 0.45, blue: 0.86))
                    
                    Text("Add Task")
                        .font(.caption)
                        .foregroundColor(.white)
                        .fontWeight(.medium)
                }
            }
            .cornerRadius(12)
        }
    }
}
