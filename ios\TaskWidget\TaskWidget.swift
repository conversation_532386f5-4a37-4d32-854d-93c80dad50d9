import WidgetKit
import SwiftUI

struct TaskWidget: Widget {
    let kind: String = "TaskWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: TaskProvider()) { entry in
            TaskWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("Task Count")
        .description("Shows the number of incomplete tasks for today")
        .supportedFamilies([.systemSmall])
    }
}

struct TaskProvider: TimelineProvider {
    func placeholder(in context: Context) -> TaskEntry {
        TaskEntry(date: Date(), taskCount: 0)
    }

    func getSnapshot(in context: Context, completion: @escaping (TaskEntry) -> ()) {
        let entry = TaskEntry(date: Date(), taskCount: getTaskCount())
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        let currentDate = Date()
        let entry = TaskEntry(date: currentDate, taskCount: getTaskCount())
        
        // Update every 30 minutes
        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 30, to: currentDate)!
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        
        completion(timeline)
    }
    
    private func getTaskCount() -> Int {
        let userDefaults = UserDefaults(suiteName: "group.app.nextlevel.widget")
        return userDefaults?.integer(forKey: "taskCount") ?? 0
    }
}

struct TaskEntry: TimelineEntry {
    let date: Date
    let taskCount: Int
}

struct TaskWidgetEntryView: View {
    var entry: TaskProvider.Entry

    var body: some View {
        ZStack {
            Color(red: 0.12, green: 0.12, blue: 0.12)
            
            VStack(spacing: 8) {
                Text("Today's Tasks")
                    .font(.caption)
                    .foregroundColor(.white)
                    .fontWeight(.medium)
                
                Text("\(entry.taskCount)")
                    .font(.title)
                    .foregroundColor(Color(red: 0.09, green: 0.45, blue: 0.86))
                    .fontWeight(.bold)
                
                Text("remaining")
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
        }
        .cornerRadius(12)
    }
}

@main
struct TaskWidgetBundle: WidgetBundle {
    var body: some Widget {
        TaskWidget()
        QuickAddWidget()
        DailyTasksWidget()
    }
}
