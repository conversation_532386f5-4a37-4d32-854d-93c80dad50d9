import WidgetKit
import SwiftUI

struct DailyTasksWidget: Widget {
    let kind: String = "DailyTasksWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: DailyTasksProvider()) { entry in
            DailyTasksWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("Daily Tasks")
        .description("Shows today's tasks")
        .supportedFamilies([.systemMedium, .systemLarge])
    }
}

struct DailyTasksProvider: TimelineProvider {
    func placeholder(in context: Context) -> DailyTasksEntry {
        DailyTasksEntry(date: Date(), tasks: [], taskCount: 0)
    }

    func getSnapshot(in context: Context, completion: @escaping (DailyTasksEntry) -> ()) {
        let entry = DailyTasksEntry(date: Date(), tasks: getTasks(), taskCount: getTaskCount())
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        let currentDate = Date()
        let entry = DailyTasksEntry(date: currentDate, tasks: getTasks(), taskCount: getTaskCount())
        
        // Update every 30 minutes
        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 30, to: currentDate)!
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        
        completion(timeline)
    }
    
    private func getTasks() -> [WidgetTask] {
        let userDefaults = UserDefaults(suiteName: "group.app.nextlevel.widget")
        guard let tasksData = userDefaults?.string(forKey: "dailyTasks"),
              let data = tasksData.data(using: .utf8),
              let jsonArray = try? JSONSerialization.jsonObject(with: data) as? [[String: Any]] else {
            return []
        }
        
        return jsonArray.compactMap { taskDict in
            guard let id = taskDict["id"] as? Int,
                  let title = taskDict["title"] as? String,
                  let type = taskDict["type"] as? String,
                  let priority = taskDict["priority"] as? Int,
                  let isCompleted = taskDict["isCompleted"] as? Bool else {
                return nil
            }
            
            let timeString = taskDict["timeString"] as? String
            
            return WidgetTask(
                id: id,
                title: title,
                type: type,
                priority: priority,
                isCompleted: isCompleted,
                timeString: timeString
            )
        }
    }
    
    private func getTaskCount() -> Int {
        let userDefaults = UserDefaults(suiteName: "group.app.nextlevel.widget")
        return userDefaults?.integer(forKey: "taskCount") ?? 0
    }
}

struct DailyTasksEntry: TimelineEntry {
    let date: Date
    let tasks: [WidgetTask]
    let taskCount: Int
}

struct WidgetTask {
    let id: Int
    let title: String
    let type: String
    let priority: Int
    let isCompleted: Bool
    let timeString: String?
}

struct DailyTasksWidgetEntryView: View {
    var entry: DailyTasksProvider.Entry

    var body: some View {
        ZStack {
            Color(red: 0.12, green: 0.12, blue: 0.12)
            
            VStack(alignment: .leading, spacing: 8) {
                // Header
                HStack {
                    Text("Today's Tasks")
                        .font(.headline)
                        .foregroundColor(.white)
                        .fontWeight(.bold)
                    
                    Spacer()
                    
                    Text("\(entry.taskCount)")
                        .font(.caption)
                        .foregroundColor(Color(red: 0.09, green: 0.45, blue: 0.86))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.gray.opacity(0.3))
                        .cornerRadius(8)
                }
                
                if entry.tasks.isEmpty {
                    Spacer()
                    Text("No tasks for today")
                        .font(.caption)
                        .foregroundColor(.gray)
                        .frame(maxWidth: .infinity, alignment: .center)
                    Spacer()
                } else {
                    // Tasks list
                    VStack(alignment: .leading, spacing: 4) {
                        ForEach(Array(entry.tasks.prefix(5).enumerated()), id: \.offset) { index, task in
                            TaskRowView(task: task)
                        }
                    }
                    
                    Spacer()
                }
            }
            .padding()
        }
        .cornerRadius(12)
    }
}

struct TaskRowView: View {
    let task: WidgetTask
    
    var body: some View {
        Link(destination: URL(string: "nextlevel://task?id=\(task.id)&action=toggle")!) {
            HStack(spacing: 8) {
                // Task type icon
                Image(systemName: taskIcon)
                    .font(.caption)
                    .foregroundColor(.white)
                    .frame(width: 16, height: 16)
                
                // Task title
                Text(task.title)
                    .font(.caption)
                    .foregroundColor(.white)
                    .lineLimit(1)
                
                Spacer()
                
                // Time if available
                if let timeString = task.timeString {
                    Text(timeString)
                        .font(.caption2)
                        .foregroundColor(.gray)
                }
                
                // Priority indicator
                Rectangle()
                    .fill(priorityColor)
                    .frame(width: 3, height: 16)
            }
            .padding(.horizontal, 6)
            .padding(.vertical, 4)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(6)
        }
    }
    
    private var taskIcon: String {
        switch task.type {
        case "COUNTER":
            return "plus.circle"
        case "TIMER":
            return "timer"
        default:
            return "checkmark.square"
        }
    }
    
    private var priorityColor: Color {
        switch task.priority {
        case 1:
            return .red
        case 2:
            return .orange
        default:
            return Color(red: 0.09, green: 0.45, blue: 0.86)
        }
    }
}
