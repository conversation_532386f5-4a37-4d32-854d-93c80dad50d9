import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:home_widget/home_widget.dart';
import 'package:next_level/Provider/task_provider.dart';
import 'package:next_level/Core/extensions.dart';

class HomeWidgetService {
  static const String appGroupId = 'app.nextlevel.widget';
  static const String taskCountKey = 'taskCount';
  static const String taskTitlesKey = 'taskTitles';

  static Future<void> updateTaskCount() async {
    final taskProvider = TaskProvider();

    // Get today's tasks - we need to get incomplete tasks only
    final todayTasks = taskProvider.taskList.where((task) => task.taskDate?.isSameDay(DateTime.now()) == true && task.routineID == null && task.status == null).toList();

    final routineTasks = taskProvider.taskList.where((task) => task.taskDate?.isSameDay(DateTime.now()) == true && task.routineID != null && task.status == null).toList();

    final allIncompleteTasks = [...todayTasks, ...routineTasks];
    final incompleteTasks = allIncompleteTasks.length;

    // Get all task titles for display (no limit, scrollable)
    final taskTitles = allIncompleteTasks.map((task) => task.title).toList();

    debugPrint('Widget Update Debug:');
    debugPrint('Today tasks: ${todayTasks.length}');
    debugPrint('Routine tasks: ${routineTasks.length}');
    debugPrint('Total incomplete: $incompleteTasks');
    debugPrint('Task titles: $taskTitles');

    try {
      await HomeWidget.saveWidgetData(taskCountKey, incompleteTasks);
      await HomeWidget.saveWidgetData(taskTitlesKey, jsonEncode(taskTitles));
      await HomeWidget.updateWidget(
        androidName: 'TaskWidgetProvider',
      );
    } catch (e) {
      debugPrint('Error updating task count widget: $e');
    }
  }

  static Future<void> updateAllWidgets() async {
    await updateTaskCount();
  }

  static Future<void> setupHomeWidget() async {
    await HomeWidget.setAppGroupId(appGroupId);
  }

  // reset
  static Future<void> resetHomeWidget() async {
    await HomeWidget.saveWidgetData(taskCountKey, -1);
    await HomeWidget.updateWidget(
      androidName: 'TaskWidgetProvider',
    );
  }
}
