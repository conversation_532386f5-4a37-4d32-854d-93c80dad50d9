import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:home_widget/home_widget.dart';
import 'package:next_level/Provider/task_provider.dart';
import 'package:next_level/Model/task_model.dart';
import 'package:next_level/Enum/task_status_enum.dart';
import 'package:next_level/Enum/task_type_enum.dart';

class HomeWidgetService {
  static const String appGroupId = 'app.nextlevel.widget';
  static const String taskCountKey = 'taskCount';
  static const String dailyTasksKey = 'dailyTasks';
  static const String lastUpdateKey = 'lastUpdate';

  static Future<void> updateTaskCount() async {
    final taskProvider = TaskProvider();
    final todayTasks = taskProvider.getTasksForDate(DateTime.now());
    final routineTasks = taskProvider.getRoutineTasksForDate(DateTime.now());

    final incompleteTasks = todayTasks.where((task) => task.status == null).length + routineTasks.where((task) => task.status == null).length;

    try {
      await HomeWidget.saveWidgetData(taskCountKey, incompleteTasks);
      await HomeWidget.updateWidget(
        androidName: 'TaskWidgetProvider',
        iOSName: 'TaskWidget',
      );
    } catch (e) {
      debugPrint('Error updating widget: $e');
    }
  }

  static Future<void> updateDailyTasksWidget() async {
    final taskProvider = TaskProvider();
    final todayTasks = taskProvider.getTasksForDate(DateTime.now());
    final routineTasks = taskProvider.getRoutineTasksForDate(DateTime.now());

    // Combine and limit to first 5 tasks for widget display
    final allTasks = [...todayTasks, ...routineTasks];
    final limitedTasks = allTasks.take(5).toList();

    // Convert tasks to widget-friendly format
    final tasksData = limitedTasks.map((task) => _taskToWidgetData(task)).toList();

    try {
      await HomeWidget.saveWidgetData(dailyTasksKey, jsonEncode(tasksData));
      await HomeWidget.saveWidgetData(lastUpdateKey, DateTime.now().millisecondsSinceEpoch);
      await HomeWidget.updateWidget(
        androidName: 'DailyTasksWidgetProvider',
        iOSName: 'DailyTasksWidget',
      );
    } catch (e) {
      debugPrint('Error updating daily tasks widget: $e');
    }
  }

  static Future<void> updateQuickAddWidget() async {
    try {
      await HomeWidget.updateWidget(
        androidName: 'QuickAddWidgetProvider',
        iOSName: 'QuickAddWidget',
      );
    } catch (e) {
      debugPrint('Error updating quick add widget: $e');
    }
  }

  static Future<void> updateAllWidgets() async {
    await Future.wait([
      updateTaskCount(),
      updateDailyTasksWidget(),
      updateQuickAddWidget(),
    ]);
  }

  static Map<String, dynamic> _taskToWidgetData(TaskModel task) {
    return {
      'id': task.id,
      'title': task.title.length > 30 ? '${task.title.substring(0, 30)}...' : task.title,
      'type': task.type.name,
      'status': task.status?.name,
      'priority': task.priority,
      'isCompleted': task.status != null,
      'hasTime': task.time != null,
      'timeString': task.time != null ? '${task.time!.hour.toString().padLeft(2, '0')}:${task.time!.minute.toString().padLeft(2, '0')}' : null,
      'progress': _getTaskProgress(task),
    };
  }

  static double _getTaskProgress(TaskModel task) {
    switch (task.type) {
      case TaskTypeEnum.CHECKBOX:
        return task.status == TaskStatusEnum.COMPLETED ? 1.0 : 0.0;
      case TaskTypeEnum.COUNTER:
        if (task.targetCount != null && task.targetCount! > 0) {
          return (task.currentCount ?? 0) / task.targetCount!;
        }
        return 0.0;
      case TaskTypeEnum.TIMER:
        if (task.remainingDuration != null && task.remainingDuration!.inSeconds > 0) {
          return (task.currentDuration?.inSeconds ?? 0) / task.remainingDuration!.inSeconds;
        }
        return 0.0;
    }
  }

  static Future<void> setupHomeWidget() async {
    await HomeWidget.setAppGroupId(appGroupId);
  }

  // reset
  static Future<void> resetHomeWidget() async {
    await HomeWidget.saveWidgetData(taskCountKey, -1);
    await HomeWidget.saveWidgetData(dailyTasksKey, '[]');
    await HomeWidget.updateWidget(
      androidName: 'TaskWidgetProvider',
      iOSName: 'TaskWidget',
    );
    await HomeWidget.updateWidget(
      androidName: 'DailyTasksWidgetProvider',
      iOSName: 'DailyTasksWidget',
    );
    await HomeWidget.updateWidget(
      androidName: 'QuickAddWidgetProvider',
      iOSName: 'QuickAddWidget',
    );
  }
}
