package app.nextlevel

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.view.View
import android.widget.RemoteViews
import es.antonborri.home_widget.HomeWidgetPlugin
import org.json.JSONArray

class TaskWidgetProvider : AppWidgetProvider() {
    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
        for (appWidgetId in appWidgetIds) {
            val widgetData = HomeWidgetPlugin.getData(context)
            val taskCount = widgetData.getInt("taskCount", -1)
            val taskTitlesJson = widgetData.getString("taskTitles", "[]")

            val views = RemoteViews("app.nextlevel", R.layout.task_widget)

            if (taskCount == -1) {
                // Loading state
                views.setTextViewText(R.id.task_count_text, "...")
                views.setViewVisibility(R.id.empty_state, View.VISIBLE)
                views.setTextViewText(R.id.empty_state, "Loading...")
            } else {
                // Set task count
                views.setTextViewText(R.id.task_count_text, taskCount.toString())

                try {
                    val taskTitles = JSONArray(taskTitlesJson)

                    if (taskTitles.length() == 0) {
                        // No tasks
                        views.setViewVisibility(R.id.empty_state, View.VISIBLE)
                        views.setTextViewText(R.id.empty_state, "🎉 No tasks for today!\nEnjoy your free time.")
                    } else {
                        // Hide empty state and create task list
                        views.setViewVisibility(R.id.empty_state, View.GONE)

                        // Create a formatted string with all tasks
                        val taskListText = StringBuilder()
                        for (i in 0 until taskTitles.length()) {
                            val taskTitle = taskTitles.getString(i)
                            taskListText.append("• $taskTitle")
                            if (i < taskTitles.length() - 1) {
                                taskListText.append("\n")
                            }
                        }

                        // Since we can't dynamically add views to RemoteViews,
                        // we'll use the empty_state TextView to show all tasks
                        views.setViewVisibility(R.id.empty_state, View.VISIBLE)
                        views.setTextViewText(R.id.empty_state, taskListText.toString())
                        views.setTextColor(R.id.empty_state, android.graphics.Color.WHITE)
                        views.setFloat(R.id.empty_state, "setTextSize", 13f)
                        views.setInt(R.id.empty_state, "setGravity", android.view.Gravity.START)
                    }
                } catch (e: Exception) {
                    // Error parsing JSON
                    views.setViewVisibility(R.id.empty_state, View.VISIBLE)
                    views.setTextViewText(R.id.empty_state, "❌ Error loading tasks\nPlease refresh widget")
                    views.setTextColor(R.id.empty_state, android.graphics.Color.parseColor("#FFCCCC"))
                }
            }

            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }
}