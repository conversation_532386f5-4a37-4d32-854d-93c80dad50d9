package app.nextlevel

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.widget.RemoteViews
import es.antonborri.home_widget.HomeWidgetPlugin

class TaskWidgetProvider : AppWidgetProvider() {
    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
        for (appWidgetId in appWidgetIds) {
            val widgetData = HomeWidgetPlugin.getData(context)
            val taskCount = widgetData.getInt("taskCount", -1)

            val views = RemoteViews("app.nextlevel", R.layout.task_widget).apply {
                if (taskCount == -1) {
                    setTextViewText(R.id.task_count_text, "...")
                    setTextViewText(R.id.footer_text, "loading")
                } else {
                    setTextViewText(R.id.task_count_text, taskCount.toString())
                    setTextViewText(R.id.footer_text, if (taskCount == 1) "task remaining" else "tasks remaining")
                }
            }

            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }
}