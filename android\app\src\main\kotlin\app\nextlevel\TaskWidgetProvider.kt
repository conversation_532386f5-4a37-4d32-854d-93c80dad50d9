package app.nextlevel

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.view.View
import android.widget.RemoteViews
import es.antonborri.home_widget.HomeWidgetPlugin
import org.json.JSONArray

class TaskWidgetProvider : AppWidgetProvider() {
    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
        for (appWidgetId in appWidgetIds) {
            val widgetData = HomeWidgetPlugin.getData(context)
            val taskCount = widgetData.getInt("taskCount", -1)
            val taskTitlesJson = widgetData.getString("taskTitles", "[]")

            val views = RemoteViews("app.nextlevel", R.layout.task_widget)

            if (taskCount == -1) {
                // Loading state
                views.setTextViewText(R.id.task_count_text, "...")
                views.setViewVisibility(R.id.empty_state, View.VISIBLE)
                views.setTextViewText(R.id.empty_state, "Loading...")
                hideAllTaskTitles(views)
            } else {
                // Set task count
                views.setTextViewText(R.id.task_count_text, taskCount.toString())

                try {
                    val taskTitles = JSONArray(taskTitlesJson)

                    if (taskTitles.length() == 0) {
                        // No tasks
                        views.setViewVisibility(R.id.empty_state, View.VISIBLE)
                        views.setTextViewText(R.id.empty_state, "No tasks for today")
                        hideAllTaskTitles(views)
                    } else {
                        // Show tasks
                        views.setViewVisibility(R.id.empty_state, View.GONE)

                        // Show up to 5 task titles
                        for (i in 0 until minOf(taskTitles.length(), 5)) {
                            val taskTitle = taskTitles.getString(i)
                            val titleId = getTaskTitleId(i + 1)
                            views.setTextViewText(titleId, "• $taskTitle")
                            views.setViewVisibility(titleId, View.VISIBLE)
                        }

                        // Hide unused task title views
                        for (i in taskTitles.length() until 5) {
                            views.setViewVisibility(getTaskTitleId(i + 1), View.GONE)
                        }
                    }
                } catch (e: Exception) {
                    // Error parsing JSON
                    views.setViewVisibility(R.id.empty_state, View.VISIBLE)
                    views.setTextViewText(R.id.empty_state, "Error loading tasks")
                    hideAllTaskTitles(views)
                }
            }

            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }

    private fun hideAllTaskTitles(views: RemoteViews) {
        for (i in 1..5) {
            views.setViewVisibility(getTaskTitleId(i), View.GONE)
        }
    }

    private fun getTaskTitleId(index: Int): Int = when (index) {
        1 -> R.id.task_title_1
        2 -> R.id.task_title_2
        3 -> R.id.task_title_3
        4 -> R.id.task_title_4
        5 -> R.id.task_title_5
        else -> R.id.task_title_1
    }
}