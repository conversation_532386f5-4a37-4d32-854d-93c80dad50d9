package app.nextlevel

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.widget.RemoteViews

class TaskWidgetProvider : AppWidgetProvider() {
    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
        for (appWidgetId in appWidgetIds) {
            val views = RemoteViews("app.nextlevel", R.layout.task_widget)

            // Set static test values first
            views.setTextViewText(R.id.header_text, "Today's Tasks")
            views.setTextViewText(R.id.task_count_text, "5")
            views.setTextViewText(R.id.empty_state, "• Test Task 1\n• Test Task 2\n• Test Task 3")

            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }
}