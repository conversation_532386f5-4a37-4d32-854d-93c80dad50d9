package app.nextlevel

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.view.View
import android.widget.RemoteViews
import es.antonborri.home_widget.HomeWidgetPlugin
import org.json.JSONArray
import org.json.JSONObject

class DailyTasksWidgetProvider : AppWidgetProvider() {
    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    private fun updateAppWidget(context: Context, appWidgetManager: AppWidgetManager, appWidgetId: Int) {
        val views = RemoteViews(context.packageName, R.layout.daily_tasks_widget)
        
        val widgetData = HomeWidgetPlugin.getData(context)
        val tasksJson = widgetData.getString("dailyTasks", "[]")
        val taskCount = widgetData.getInt("taskCount", 0)
        
        try {
            val tasksArray = JSONArray(tasksJson)
            
            // Update task count
            views.setTextViewText(R.id.task_count, taskCount.toString())
            
            // Hide all task items first
            for (i in 1..5) {
                views.setViewVisibility(getTaskItemId(i), View.GONE)
            }
            
            if (tasksArray.length() == 0) {
                // Show empty state
                views.setViewVisibility(R.id.empty_state, View.VISIBLE)
            } else {
                // Hide empty state
                views.setViewVisibility(R.id.empty_state, View.GONE)
                
                // Show tasks (up to 5)
                for (i in 0 until minOf(tasksArray.length(), 5)) {
                    val task = tasksArray.getJSONObject(i)
                    val itemIndex = i + 1
                    
                    // Show task item
                    views.setViewVisibility(getTaskItemId(itemIndex), View.VISIBLE)
                    
                    // Set task title
                    views.setTextViewText(getTaskTitleId(itemIndex), task.getString("title"))
                    
                    // Set task icon based on type
                    val iconRes = when (task.getString("type")) {
                        "COUNTER" -> R.drawable.ic_counter
                        "TIMER" -> R.drawable.ic_timer
                        else -> R.drawable.ic_checkbox
                    }
                    views.setImageViewResource(getTaskIconId(itemIndex), iconRes)
                    
                    // Set priority indicator color
                    val priorityColor = when (task.getInt("priority")) {
                        1 -> 0xFFFF4444.toInt() // High priority - Red
                        2 -> 0xFFFFAA00.toInt() // Medium priority - Orange
                        else -> 0xFF1773DB.toInt() // Low priority - Blue
                    }
                    views.setInt(getPriorityIndicatorId(itemIndex), "setBackgroundColor", priorityColor)
                    
                    // Set time if available
                    if (task.has("timeString") && !task.isNull("timeString")) {
                        views.setTextViewText(getTaskTimeId(itemIndex), task.getString("timeString"))
                        views.setViewVisibility(getTaskTimeId(itemIndex), View.VISIBLE)
                    } else {
                        views.setViewVisibility(getTaskTimeId(itemIndex), View.GONE)
                    }
                    
                    // Set click listener for task completion
                    val taskId = task.getInt("id")
                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse("nextlevel://task?id=$taskId&action=toggle"))
                    intent.setPackage(context.packageName)
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                    
                    val pendingIntent = PendingIntent.getActivity(
                        context,
                        taskId,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                    )
                    
                    views.setOnClickPendingIntent(getTaskItemId(itemIndex), pendingIntent)
                }
            }
            
        } catch (e: Exception) {
            // Show error state
            views.setViewVisibility(R.id.empty_state, View.VISIBLE)
            views.setTextViewText(R.id.empty_state, "Error loading tasks")
        }
        
        appWidgetManager.updateAppWidget(appWidgetId, views)
    }
    
    private fun getTaskItemId(index: Int): Int = when (index) {
        1 -> R.id.task_item_1
        2 -> R.id.task_item_2
        3 -> R.id.task_item_3
        4 -> R.id.task_item_4
        5 -> R.id.task_item_5
        else -> R.id.task_item_1
    }
    
    private fun getTaskTitleId(index: Int): Int = when (index) {
        1 -> R.id.task_title_1
        2 -> R.id.task_title_2
        3 -> R.id.task_title_3
        4 -> R.id.task_title_4
        5 -> R.id.task_title_5
        else -> R.id.task_title_1
    }
    
    private fun getTaskIconId(index: Int): Int = when (index) {
        1 -> R.id.task_icon_1
        2 -> R.id.task_icon_2
        3 -> R.id.task_icon_3
        4 -> R.id.task_icon_4
        5 -> R.id.task_icon_5
        else -> R.id.task_icon_1
    }
    
    private fun getTaskTimeId(index: Int): Int = when (index) {
        1 -> R.id.task_time_1
        2 -> R.id.task_time_2
        3 -> R.id.task_time_3
        4 -> R.id.task_time_4
        5 -> R.id.task_time_5
        else -> R.id.task_time_1
    }
    
    private fun getPriorityIndicatorId(index: Int): Int = when (index) {
        1 -> R.id.priority_indicator_1
        2 -> R.id.priority_indicator_2
        3 -> R.id.priority_indicator_3
        4 -> R.id.priority_indicator_4
        5 -> R.id.priority_indicator_5
        else -> R.id.priority_indicator_1
    }
}
