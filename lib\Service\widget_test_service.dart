import 'package:flutter/foundation.dart';
import 'package:next_level/Service/home_widget_service.dart';
import 'package:next_level/Provider/task_provider.dart';
import 'package:next_level/Model/task_model.dart';
import 'package:next_level/Enum/task_type_enum.dart';

class WidgetTestService {
  static Future<void> createTestTasks() async {
    if (kDebugMode) {
      final taskProvider = TaskProvider();
      
      // Create some test tasks for today
      final testTasks = [
        TaskModel(
          id: 9999,
          title: "Test Checkbox Task",
          type: TaskTypeEnum.CHECKBOX,
          taskDate: DateTime.now(),
          isNotificationOn: false,
          isAlarmOn: false,
          priority: 1,
        ),
        TaskModel(
          id: 9998,
          title: "Test Counter Task",
          type: TaskTypeEnum.COUNTER,
          taskDate: DateTime.now(),
          isNotificationOn: false,
          isAlarmOn: false,
          priority: 2,
          targetCount: 5,
          currentCount: 2,
        ),
        TaskModel(
          id: 9997,
          title: "Test Timer Task",
          type: TaskTypeEnum.TIMER,
          taskDate: DateTime.now(),
          isNotificationOn: false,
          isAlarmOn: false,
          priority: 3,
          remainingDuration: const Duration(minutes: 30),
          currentDuration: const Duration(minutes: 10),
        ),
      ];
      
      // Add test tasks to the list (don't save to database)
      taskProvider.taskList.addAll(testTasks);
      
      // Update widgets with test data
      await HomeWidgetService.updateAllWidgets();
      
      debugPrint('Created test tasks for widget testing');
    }
  }
  
  static Future<void> removeTestTasks() async {
    if (kDebugMode) {
      final taskProvider = TaskProvider();
      
      // Remove test tasks
      taskProvider.taskList.removeWhere((task) => 
        task.id >= 9997 && task.id <= 9999);
      
      // Update widgets
      await HomeWidgetService.updateAllWidgets();
      
      debugPrint('Removed test tasks');
    }
  }
  
  static Future<void> testWidgetUpdate() async {
    if (kDebugMode) {
      debugPrint('Testing widget update...');
      
      await createTestTasks();
      
      // Wait a bit then remove test tasks
      await Future.delayed(const Duration(seconds: 10));
      await removeTestTasks();
      
      debugPrint('Widget test completed');
    }
  }
}
