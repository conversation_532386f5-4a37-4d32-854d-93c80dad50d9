import 'package:flutter/foundation.dart';
import 'package:home_widget/home_widget.dart';
import 'package:next_level/Page/Home/Add%20Task/add_task_page.dart';
import 'package:next_level/Provider/task_provider.dart';
import 'package:next_level/Service/navigator_service.dart';
import 'package:next_level/Service/home_widget_service.dart';
import 'package:next_level/Model/task_model.dart';
import 'package:next_level/Enum/task_status_enum.dart';

class DeepLinkService {
  static final DeepLinkService _instance = DeepLinkService._internal();
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();

  static void initialize() {
    // Listen for widget clicks
    HomeWidget.widgetClicked.listen((uri) {
      if (uri != null) {
        handleDeepLink(uri);
      }
    });
  }

  static void handleDeepLink(Uri uri) {
    debugPrint('Handling deep link: $uri');

    switch (uri.host) {
      case 'quickadd':
        _openAddTaskPage();
        break;
      case 'task':
        final taskId = int.tryParse(uri.queryParameters['id'] ?? '');
        final action = uri.queryParameters['action'] ?? 'toggle';
        if (taskId != null) {
          _handleTaskAction(taskId, action);
        }
        break;
      case 'open':
        _openApp();
        break;
      default:
        debugPrint('Unknown deep link host: ${uri.host}');
    }
  }

  static void _openAddTaskPage() {
    try {
      // Navigate to AddTaskPage (it will clear data in initState)
      NavigatorService().goTo(const AddTaskPage());

      debugPrint('Opened AddTaskPage from widget');
    } catch (e) {
      debugPrint('Error opening AddTaskPage from widget: $e');
    }
  }

  static void _handleTaskAction(int taskId, String action) {
    try {
      final taskProvider = TaskProvider();
      final task = taskProvider.taskList.firstWhere(
        (task) => task.id == taskId,
        orElse: () => throw Exception('Task not found'),
      );

      switch (action) {
        case 'toggle':
          _toggleTaskStatus(task);
          break;
        case 'complete':
          _completeTask(task);
          break;
        case 'open':
          _openTaskDetail(task);
          break;
        default:
          debugPrint('Unknown task action: $action');
      }
    } catch (e) {
      debugPrint('Error handling task action: $e');
    }
  }

  static void _toggleTaskStatus(TaskModel task) {
    final taskProvider = TaskProvider();

    if (task.status == null) {
      // Mark as completed
      taskProvider.completeRoutine(task);
    } else if (task.status == TaskStatusEnum.COMPLETED) {
      // Mark as in progress (null status)
      task.status = null;
      taskProvider.updateItems();
    }

    // Update widgets after status change
    HomeWidgetService.updateAllWidgets();

    debugPrint('Toggled task status for: ${task.title}');
  }

  static void _completeTask(TaskModel task) {
    final taskProvider = TaskProvider();
    taskProvider.completeRoutine(task);

    // Update widgets after completion
    HomeWidgetService.updateAllWidgets();

    debugPrint('Completed task from widget: ${task.title}');
  }

  static void _openTaskDetail(TaskModel task) {
    try {
      // Navigate to task detail page
      // This would need to be implemented based on your existing task detail navigation
      debugPrint('Opening task detail for: ${task.title}');
    } catch (e) {
      debugPrint('Error opening task detail: $e');
    }
  }

  static void _openApp() {
    // Just bring the app to foreground
    // The app will show the current page
    debugPrint('Opening app from widget');
  }

  // Helper method to create deep link URIs for widgets
  static String createTaskActionUri(int taskId, String action) {
    return 'nextlevel://task?id=$taskId&action=$action';
  }

  static String createQuickAddUri() {
    return 'nextlevel://quickadd';
  }

  static String createOpenAppUri() {
    return 'nextlevel://open';
  }
}
