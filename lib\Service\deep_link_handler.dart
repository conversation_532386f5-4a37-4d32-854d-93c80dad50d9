import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:next_level/Service/deep_link_service.dart';

class DeepLinkHandler {
  static const MethodChannel _channel = MethodChannel('app.nextlevel/deep_link');
  static bool _isInitialized = false;

  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Set up method call handler for deep links from native side
      _channel.setMethodCallHandler(_handleMethodCall);
      
      // Check for initial deep link when app starts
      final String? initialLink = await _channel.invokeMethod('getInitialLink');
      if (initialLink != null && initialLink.isNotEmpty) {
        _handleDeepLink(initialLink);
      }
      
      _isInitialized = true;
      debugPrint('Deep link handler initialized');
    } catch (e) {
      debugPrint('Error initializing deep link handler: $e');
    }
  }

  static Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onDeepLink':
        final String? link = call.arguments as String?;
        if (link != null) {
          _handleDeepLink(link);
        }
        break;
      default:
        debugPrint('Unknown method call: ${call.method}');
    }
  }

  static void _handleDeepLink(String link) {
    try {
      final uri = Uri.parse(link);
      DeepLinkService.handleDeepLink(uri);
    } catch (e) {
      debugPrint('Error parsing deep link: $link, error: $e');
    }
  }
}
