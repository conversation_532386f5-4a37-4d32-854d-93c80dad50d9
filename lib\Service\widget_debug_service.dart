import 'package:flutter/foundation.dart';
import 'package:next_level/Provider/task_provider.dart';
import 'package:next_level/Core/extensions.dart';
import 'package:next_level/Service/home_widget_service.dart';

class WidgetDebugService {
  static void debugWidgetData() {
    if (kDebugMode) {
      debugPrint('=== WIDGET DEBUG SERVICE ===');
      
      final taskProvider = TaskProvider();
      final today = DateTime.now();
      
      debugPrint('Today: $today');
      debugPrint('Total tasks in taskList: ${taskProvider.taskList.length}');
      
      // Debug all tasks
      for (var task in taskProvider.taskList) {
        debugPrint('Task: ${task.title}');
        debugPrint('  ID: ${task.id}');
        debugPrint('  Date: ${task.taskDate}');
        debugPrint('  Is today: ${task.taskDate?.isSameDay(today)}');
        debugPrint('  Routine ID: ${task.routineID}');
        debugPrint('  Status: ${task.status}');
        debugPrint('  Is incomplete: ${task.status == null}');
      }
      
      // Debug today's tasks using our widget logic
      final todayTasks = taskProvider.taskList.where((task) => 
        task.taskDate?.isSameDay(today) == true && 
        task.routineID == null && 
        task.status == null
      ).toList();
      
      final routineTasks = taskProvider.taskList.where((task) => 
        task.taskDate?.isSameDay(today) == true && 
        task.routineID != null && 
        task.status == null
      ).toList();
      
      debugPrint('Today tasks (non-routine, incomplete): ${todayTasks.length}');
      for (var task in todayTasks) {
        debugPrint('  - ${task.title}');
      }
      
      debugPrint('Routine tasks (today, incomplete): ${routineTasks.length}');
      for (var task in routineTasks) {
        debugPrint('  - ${task.title}');
      }
      
      final allIncompleteTasks = [...todayTasks, ...routineTasks];
      debugPrint('Total incomplete tasks for today: ${allIncompleteTasks.length}');
      
      // Test widget update
      debugPrint('Triggering widget update...');
      HomeWidgetService.updateAllWidgets();
    }
  }
  
  static void debugTaskProvider() {
    if (kDebugMode) {
      debugPrint('=== TASK PROVIDER DEBUG ===');
      
      final taskProvider = TaskProvider();
      final today = DateTime.now();
      
      // Test the original methods
      final originalTodayTasks = taskProvider.getTasksForDate(today);
      final originalRoutineTasks = taskProvider.getRoutineTasksForDate(today);
      
      debugPrint('Original getTasksForDate: ${originalTodayTasks.length}');
      debugPrint('Original getRoutineTasksForDate: ${originalRoutineTasks.length}');
      debugPrint('showCompleted setting: ${taskProvider.showCompleted}');
      
      for (var task in originalTodayTasks) {
        debugPrint('  Task: ${task.title} - Status: ${task.status}');
      }
      
      for (var task in originalRoutineTasks) {
        debugPrint('  Routine: ${task.title} - Status: ${task.status}');
      }
    }
  }
}
