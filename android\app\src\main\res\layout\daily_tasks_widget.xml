<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="12dp"
    android:background="@drawable/widget_background">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <TextView
            android:id="@+id/header_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Today's Tasks"
            android:textSize="16sp"
            android:textColor="#FFFFFF"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/task_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0"
            android:textSize="14sp"
            android:textColor="#1773DB"
            android:background="@drawable/count_background"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp"
            android:minWidth="24dp"
            android:gravity="center" />

    </LinearLayout>

    <!-- Tasks List Container -->
    <LinearLayout
        android:id="@+id/tasks_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Task Item 1 -->
        <LinearLayout
            android:id="@+id/task_item_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="6dp"
            android:background="@drawable/task_item_background"
            android:layout_marginBottom="4dp"
            android:visibility="gone">

            <ImageView
                android:id="@+id/task_icon_1"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_checkbox"
                android:layout_marginEnd="8dp"
                android:tint="#FFFFFF" />

            <TextView
                android:id="@+id/task_title_1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Task Title"
                android:textSize="12sp"
                android:textColor="#FFFFFF"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/task_time_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10:00"
                android:textSize="10sp"
                android:textColor="#CCCCCC"
                android:layout_marginStart="4dp"
                android:visibility="gone" />

            <View
                android:id="@+id/priority_indicator_1"
                android:layout_width="4dp"
                android:layout_height="16dp"
                android:background="#1773DB"
                android:layout_marginStart="6dp" />

        </LinearLayout>

        <!-- Task Item 2 -->
        <LinearLayout
            android:id="@+id/task_item_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="6dp"
            android:background="@drawable/task_item_background"
            android:layout_marginBottom="4dp"
            android:visibility="gone">

            <ImageView
                android:id="@+id/task_icon_2"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_checkbox"
                android:layout_marginEnd="8dp"
                android:tint="#FFFFFF" />

            <TextView
                android:id="@+id/task_title_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Task Title"
                android:textSize="12sp"
                android:textColor="#FFFFFF"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/task_time_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10:00"
                android:textSize="10sp"
                android:textColor="#CCCCCC"
                android:layout_marginStart="4dp"
                android:visibility="gone" />

            <View
                android:id="@+id/priority_indicator_2"
                android:layout_width="4dp"
                android:layout_height="16dp"
                android:background="#1773DB"
                android:layout_marginStart="6dp" />

        </LinearLayout>

        <!-- Task Item 3 -->
        <LinearLayout
            android:id="@+id/task_item_3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="6dp"
            android:background="@drawable/task_item_background"
            android:layout_marginBottom="4dp"
            android:visibility="gone">

            <ImageView
                android:id="@+id/task_icon_3"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_checkbox"
                android:layout_marginEnd="8dp"
                android:tint="#FFFFFF" />

            <TextView
                android:id="@+id/task_title_3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Task Title"
                android:textSize="12sp"
                android:textColor="#FFFFFF"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/task_time_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10:00"
                android:textSize="10sp"
                android:textColor="#CCCCCC"
                android:layout_marginStart="4dp"
                android:visibility="gone" />

            <View
                android:id="@+id/priority_indicator_3"
                android:layout_width="4dp"
                android:layout_height="16dp"
                android:background="#1773DB"
                android:layout_marginStart="6dp" />

        </LinearLayout>

        <!-- Task Item 4 -->
        <LinearLayout
            android:id="@+id/task_item_4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="6dp"
            android:background="@drawable/task_item_background"
            android:layout_marginBottom="4dp"
            android:visibility="gone">

            <ImageView
                android:id="@+id/task_icon_4"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_checkbox"
                android:layout_marginEnd="8dp"
                android:tint="#FFFFFF" />

            <TextView
                android:id="@+id/task_title_4"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Task Title"
                android:textSize="12sp"
                android:textColor="#FFFFFF"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/task_time_4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10:00"
                android:textSize="10sp"
                android:textColor="#CCCCCC"
                android:layout_marginStart="4dp"
                android:visibility="gone" />

            <View
                android:id="@+id/priority_indicator_4"
                android:layout_width="4dp"
                android:layout_height="16dp"
                android:background="#1773DB"
                android:layout_marginStart="6dp" />

        </LinearLayout>

        <!-- Task Item 5 -->
        <LinearLayout
            android:id="@+id/task_item_5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="6dp"
            android:background="@drawable/task_item_background"
            android:layout_marginBottom="4dp"
            android:visibility="gone">

            <ImageView
                android:id="@+id/task_icon_5"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_checkbox"
                android:layout_marginEnd="8dp"
                android:tint="#FFFFFF" />

            <TextView
                android:id="@+id/task_title_5"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Task Title"
                android:textSize="12sp"
                android:textColor="#FFFFFF"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/task_time_5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10:00"
                android:textSize="10sp"
                android:textColor="#CCCCCC"
                android:layout_marginStart="4dp"
                android:visibility="gone" />

            <View
                android:id="@+id/priority_indicator_5"
                android:layout_width="4dp"
                android:layout_height="16dp"
                android:background="#1773DB"
                android:layout_marginStart="6dp" />

        </LinearLayout>

        <!-- Empty State -->
        <TextView
            android:id="@+id/empty_state"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="No tasks for today"
            android:textSize="14sp"
            android:textColor="#CCCCCC"
            android:gravity="center"
            android:layout_gravity="center"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>
