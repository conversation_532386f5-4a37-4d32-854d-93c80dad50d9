<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:orientation="vertical"
	android:padding="8dp"
	android:background="@drawable/widget_background">

	<!-- Header with count -->
	<LinearLayout
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:orientation="horizontal"
		android:gravity="center_vertical"
		android:layout_marginBottom="8dp">

		<TextView
			android:id="@+id/header_text"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_weight="1"
			android:text="Today's Tasks"
			android:textSize="14sp"
			android:textColor="#FFFFFF"
			android:textStyle="bold" />

		<TextView
			android:id="@+id/task_count_text"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:textSize="18sp"
			android:textColor="#1773DB"
			android:text="0"
			android:textStyle="bold"
			android:background="#333333"
			android:paddingHorizontal="8dp"
			android:paddingVertical="2dp"
			android:minWidth="24dp"
			android:gravity="center" />

	</LinearLayout>

	<!-- Task list container -->
	<LinearLayout
		android:id="@+id/task_list_container"
		android:layout_width="match_parent"
		android:layout_height="0dp"
		android:layout_weight="1"
		android:orientation="vertical">

		<!-- Task 1 -->
		<TextView
			android:id="@+id/task_title_1"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:text="Task 1"
			android:textSize="12sp"
			android:textColor="#FFFFFF"
			android:maxLines="1"
			android:ellipsize="end"
			android:layout_marginBottom="2dp"
			android:visibility="gone" />

		<!-- Task 2 -->
		<TextView
			android:id="@+id/task_title_2"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:text="Task 2"
			android:textSize="12sp"
			android:textColor="#FFFFFF"
			android:maxLines="1"
			android:ellipsize="end"
			android:layout_marginBottom="2dp"
			android:visibility="gone" />

		<!-- Task 3 -->
		<TextView
			android:id="@+id/task_title_3"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:text="Task 3"
			android:textSize="12sp"
			android:textColor="#FFFFFF"
			android:maxLines="1"
			android:ellipsize="end"
			android:layout_marginBottom="2dp"
			android:visibility="gone" />

		<!-- Task 4 -->
		<TextView
			android:id="@+id/task_title_4"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:text="Task 4"
			android:textSize="12sp"
			android:textColor="#FFFFFF"
			android:maxLines="1"
			android:ellipsize="end"
			android:layout_marginBottom="2dp"
			android:visibility="gone" />

		<!-- Task 5 -->
		<TextView
			android:id="@+id/task_title_5"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:text="Task 5"
			android:textSize="12sp"
			android:textColor="#FFFFFF"
			android:maxLines="1"
			android:ellipsize="end"
			android:layout_marginBottom="2dp"
			android:visibility="gone" />

		<!-- Empty state -->
		<TextView
			android:id="@+id/empty_state"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:text="No tasks for today"
			android:textSize="12sp"
			android:textColor="#CCCCCC"
			android:gravity="center"
			android:layout_gravity="center"
			android:visibility="gone" />

	</LinearLayout>

</LinearLayout>