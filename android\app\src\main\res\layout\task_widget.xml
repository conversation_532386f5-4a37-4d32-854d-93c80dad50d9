<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:orientation="vertical"
	android:padding="12dp"
	android:background="@drawable/widget_background">

	<!-- Header with count -->
	<LinearLayout
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:orientation="horizontal"
		android:gravity="center_vertical"
		android:layout_marginBottom="12dp"
		android:background="#2A2A2A"
		android:padding="8dp"
		android:elevation="2dp">

		<ImageView
			android:layout_width="20dp"
			android:layout_height="20dp"
			android:src="@android:drawable/ic_menu_agenda"
			android:tint="#1773DB"
			android:layout_marginEnd="8dp" />

		<TextView
			android:id="@+id/header_text"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_weight="1"
			android:text="Today's Tasks"
			android:textSize="16sp"
			android:textColor="#FFFFFF"
			android:textStyle="bold" />

		<TextView
			android:id="@+id/task_count_text"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:textSize="16sp"
			android:textColor="#FFFFFF"
			android:text="0"
			android:textStyle="bold"
			android:background="#1773DB"
			android:paddingHorizontal="10dp"
			android:paddingVertical="4dp"
			android:minWidth="28dp"
			android:gravity="center" />

	</LinearLayout>

	<!-- Scrollable task list -->
	<ScrollView
		android:layout_width="match_parent"
		android:layout_height="0dp"
		android:layout_weight="1"
		android:scrollbars="vertical"
		android:fadeScrollbars="false">

		<LinearLayout
			android:id="@+id/task_list_container"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:orientation="vertical"
			android:paddingBottom="4dp">

			<!-- Task list display (will show all tasks or empty state) -->
			<TextView
				android:id="@+id/empty_state"
				android:layout_width="match_parent"
				android:layout_height="wrap_content"
				android:text="No tasks for today"
				android:textSize="13sp"
				android:textColor="#FFFFFF"
				android:gravity="start"
				android:padding="8dp"
				android:lineSpacingExtra="4dp"
				android:visibility="gone" />

			<!-- Dynamic task items will be added here programmatically -->

		</LinearLayout>

	</ScrollView>

</LinearLayout>