<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:orientation="vertical"
	android:gravity="center"
	android:padding="8dp"
	android:background="@drawable/widget_background">

	<TextView
		android:id="@+id/header_text"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:text="Today's Tasks"
		android:textSize="12sp"
		android:textColor="#FFFFFF"
		android:gravity="center"
		android:layout_marginBottom="4dp" />

	<TextView
		android:id="@+id/task_count_text"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:textSize="24sp"
		android:textColor="#1773DB"
		android:gravity="center"
		android:text="0"
		android:textStyle="bold" />

	<TextView
		android:id="@+id/footer_text"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:text="remaining"
		android:textSize="10sp"
		android:textColor="#CCCCCC"
		android:gravity="center"
		android:layout_marginTop="2dp" />

</LinearLayout>