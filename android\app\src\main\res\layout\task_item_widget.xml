<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="8dp"
    android:layout_marginBottom="4dp"
    android:background="@drawable/task_item_background">

    <!-- Task bullet/icon -->
    <View
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:background="#1773DB"
        android:layout_marginEnd="10dp" />

    <!-- Task title -->
    <TextView
        android:id="@+id/task_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Task Title"
        android:textSize="13sp"
        android:textColor="#FFFFFF"
        android:maxLines="2"
        android:ellipsize="end"
        android:lineSpacingExtra="2dp" />

    <!-- Task type indicator (optional) -->
    <ImageView
        android:id="@+id/task_type_icon"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@android:drawable/ic_menu_agenda"
        android:tint="#CCCCCC"
        android:layout_marginStart="8dp"
        android:visibility="gone" />

</LinearLayout>
