# Flutter Home Screen Widgets Setup Guide

This guide explains how to set up and use the Flutter home screen widgets for the NextLevel task management app.

## Overview

The app now includes three home screen widgets:

1. **Quick Add Widget** - A simple button to quickly add new tasks
2. **Daily Tasks Widget** - Shows today's tasks with completion status and priority
3. **Task Count Widget** - Shows the number of incomplete tasks (existing widget, enhanced)

## Features

### Quick Add Widget
- Simple "+" button design matching app theme
- <PERSON><PERSON> open the app directly to the AddTaskPage
- Minimal, clean design with app colors
- Available on both Android and iOS

### Daily Tasks Widget
- Displays up to 5 of today's tasks and routines
- Shows task titles, types (checkbox/counter/timer), and priority indicators
- Displays time if scheduled
- Tap tasks to mark as complete directly from widget
- Shows empty state when no tasks
- Priority color coding: Red (High), Orange (Medium), Blue (Low)
- Auto-refreshes when tasks change

### Task Count Widget (Enhanced)
- Shows total number of incomplete tasks for today
- Updates automatically when task status changes
- Compact design suitable for small widget sizes

## Android Setup

### Widget Providers
- `QuickAddWidgetProvider.kt` - Handles quick add functionality
- `DailyTasksWidgetProvider.kt` - Manages daily tasks display
- `TaskWidgetProvider.kt` - Original task count widget (enhanced)

### Layouts
- `quick_add_widget.xml` - Quick add button layout
- `daily_tasks_widget.xml` - Daily tasks list layout
- `task_widget.xml` - Task count display layout

### Widget Configuration
- Widgets are registered in `AndroidManifest.xml`
- Deep link handling for widget interactions
- Custom drawable resources for icons and backgrounds

## iOS Setup

### Widget Extensions
- `TaskWidget.swift` - Task count widget
- `QuickAddWidget.swift` - Quick add button widget
- `DailyTasksWidget.swift` - Daily tasks list widget

### Widget Sizes
- Quick Add: Small widget
- Task Count: Small widget
- Daily Tasks: Medium and Large widgets

## Deep Link Integration

### Supported Deep Links
- `nextlevel://quickadd` - Opens add task page
- `nextlevel://task?id={taskId}&action=toggle` - Toggles task completion
- `nextlevel://open` - Opens main app

### Implementation
- `DeepLinkService` handles widget tap actions
- `DeepLinkHandler` manages platform-specific deep link routing
- Automatic task status updates and widget refresh

## Widget Data Management

### Data Storage
- Uses `home_widget` package for cross-platform data sharing
- Task data serialized to JSON for widget consumption
- Automatic updates when tasks change in the app

### Update Triggers
- Task creation, completion, or status change
- Task date modifications
- App startup and background refresh

## Usage Instructions

### Adding Widgets (Android)
1. Long press on home screen
2. Select "Widgets"
3. Find "NextLevel" widgets
4. Drag desired widget to home screen

### Adding Widgets (iOS)
1. Long press on home screen
2. Tap "+" in top corner
3. Search for "NextLevel"
4. Select desired widget size and add

### Widget Interactions
- **Quick Add Widget**: Tap to open add task page
- **Daily Tasks Widget**: Tap individual tasks to mark complete
- **Task Count Widget**: Tap to open main app

## Development Notes

### Widget Updates
- Widgets automatically update when tasks change
- Manual refresh available via `HomeWidgetService.updateAllWidgets()`
- Background updates every 30 minutes (iOS) or on data change (Android)

### Testing
- Use `WidgetTestService.testWidgetUpdate()` for testing
- Creates temporary test tasks for widget validation
- Debug mode only functionality

### Customization
- Widget colors match app theme (`AppColors` class)
- Priority indicators use consistent color scheme
- Task type icons match app iconography

## Troubleshooting

### Widgets Not Updating
1. Check if app has background refresh permissions
2. Verify widget data is being saved correctly
3. Force refresh by opening the app

### Deep Links Not Working
1. Ensure app is installed and can handle custom URL schemes
2. Check AndroidManifest.xml intent filters
3. Verify iOS URL scheme configuration

### Widget Display Issues
1. Check widget layout files for correct resource references
2. Verify drawable resources are properly included
3. Test on different device sizes and orientations

## Future Enhancements

### Planned Features
- Widget configuration options
- Multiple widget themes
- Task filtering in widgets
- Gesture support for task actions
- Widget-specific settings

### Performance Optimizations
- Lazy loading for large task lists
- Efficient data serialization
- Reduced widget update frequency
- Background processing optimization

## Technical Architecture

### Data Flow
1. App creates/modifies tasks
2. `TaskProvider` triggers widget updates
3. `HomeWidgetService` serializes data
4. Platform-specific widgets render data
5. User interactions trigger deep links
6. `DeepLinkService` handles actions

### Key Components
- `HomeWidgetService` - Central widget management
- `DeepLinkService` - Widget interaction handling
- Platform-specific widget providers
- JSON data serialization for cross-platform compatibility

This implementation provides a comprehensive widget system that enhances user productivity by allowing quick task management directly from the home screen.
