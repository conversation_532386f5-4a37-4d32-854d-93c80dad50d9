# Android Home Screen Widget Setup Guide

This guide explains how to set up and use the Android home screen widget for the NextLevel task management app.

## Overview

The app includes one simple and effective home screen widget:

**Task Count Widget** - Shows the number of incomplete tasks for today

## Features

### Task Count Widget
- Shows total number of incomplete tasks for today
- Updates automatically when task status changes
- Clean design with app theme colors
- Displays "Today's Tasks" header with count and "remaining" footer
- Loading state shows "..." while data is being fetched
- Compact design suitable for small widget sizes

## Android Setup

### Widget Provider
- `TaskWidgetProvider.kt` - Handles task count display and updates

### Layout
- `task_widget.xml` - Task count display layout with header, count, and footer
- `widget_background.xml` - Background drawable with rounded corners

### Widget Configuration
- Widget is registered in `AndroidManifest.xml`
- Uses `home_widget` package for data sharing between Flutter and Android
- No deep link handling - simple display widget only

## Widget Data Management

### Data Storage
- Uses `home_widget` package for data sharing between Flutter and Android
- Task count stored as integer value
- Automatic updates when tasks change in the app

### Update Triggers
- Task creation, completion, or status change
- Task date modifications
- App startup and background refresh

## Usage Instructions

### Adding Widget (Android)
1. Long press on home screen
2. Select "Widgets"
3. Find "NextLevel" widget
4. Drag "Task Count" widget to home screen

### Widget Display
- Shows "Today's Tasks" as header
- Displays count of incomplete tasks in large blue text
- Shows "task remaining" or "tasks remaining" as footer
- Updates automatically when you complete or add tasks

## Development Notes

### Widget Updates
- Widget automatically updates when tasks change
- Manual refresh available via `HomeWidgetService.updateAllWidgets()`
- Updates triggered by task creation, completion, or status changes

### Customization
- Widget colors match app theme (`AppColors` class)
- Background uses rounded corners with dark theme
- Text colors: White for header/footer, Blue for count

## Troubleshooting

### Widget Not Updating
1. Check if app has background refresh permissions
2. Verify widget data is being saved correctly
3. Force refresh by opening the app
4. Remove and re-add the widget

### Widget Display Issues
1. Check widget layout files for correct resource references
2. Verify drawable resources are properly included
3. Test on different device sizes

## Technical Architecture

### Data Flow
1. App creates/modifies tasks
2. `TaskProvider` triggers widget updates via `HomeWidgetService.updateAllWidgets()`
3. `HomeWidgetService` saves task count data
4. `TaskWidgetProvider` reads data and updates widget display

### Key Components
- `HomeWidgetService` - Central widget management and data saving
- `TaskWidgetProvider` - Android widget provider that handles display
- `task_widget.xml` - Widget layout with header, count, and footer
- `widget_background.xml` - Rounded background drawable

This simplified implementation provides a clean, functional widget that shows today's task count without complexity.
